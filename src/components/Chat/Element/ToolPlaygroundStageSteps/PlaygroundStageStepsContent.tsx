/* eslint-disable max-lines */
import {useState, useEffect, useRef} from 'react';
import styled from '@emotion/styled';
import {StageStep} from '@/types/staff/stage';
import {
    renderStepContent,
    renderExpandIcon,
    renderStepIcon,
    renderInnerExpandIcon,
} from './PlaygroundStageStepsRenderers';
import {Flex} from 'antd';

interface ItemsProps {
    steps: StageStep[];
    isOuterLayer?: boolean;
}

const Container = styled.div<{isOuterLayer?: boolean}>`
    display: flex;
    flex-direction: column;
    ${props => (!props.isOuterLayer ? 'gap: 8px;' : '')}
`;

const StepContainer = styled.div<{isOuterLayer?: boolean}>`
    ${props => (props.isOuterLayer
        ? 'margin-bottom: 8px;'
        : 'background-color: #F5F7FA; border-radius: 8px; padding: 3px 16px;')}

    &:last-child {
        margin-bottom: 0;
    }
`;

const StepHeader = styled.div<{isOuterLayer?: boolean}>`
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 3px 0;
    border-radius: 4px;
    transition: background-color 0.2s;
    ${props => (props.isOuterLayer ? 'justify-content: flex-start;' : 'justify-content: space-between;')}
`;

const StepTitleWrapper = styled.div`
    display: flex;
    align-items: center;
    gap: 8px;
`;

const StepTitle = styled.div<{hasTooltip?: boolean}>`
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0px;
    text-align: justify;
    vertical-align: middle;
    color: #110f16;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    ${props => (props.hasTooltip ? 'cursor: help;' : '')}

    &:hover {
        ${props => (props.hasTooltip ? `
            position: relative;
            &::after {
                content: attr(title);
                position: absolute;
                top: 100%;
                left: 0;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                white-space: nowrap;
                z-index: 1000;
                margin-top: 4px;
            }
        ` : '')}
    }
`;

const StatusBadge = styled.div<{isOuterLayer?: boolean}>`
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #EB4561;
    flex-shrink: 0;
    ${props => (props.isOuterLayer ? `
        background-color: #FFDCE2;
        border-radius: 44px;
        padding: 0 6px;
        min-width: 32px;
        text-align: center;
    ` : '')}
`;


interface StepItemProps {
    step: StageStep;
    isOuterLayer: boolean;
}

export const PlaygroundStageStepsContent = ({
    steps,
    isOuterLayer = true,
}: ItemsProps) => {
    return (
        <Container isOuterLayer={isOuterLayer}>
            {steps?.map(step => (
                // eslint-disable-next-line @typescript-eslint/no-use-before-define
                <StepItem
                    key={step.stepId}
                    step={step}
                    isOuterLayer={isOuterLayer}
                />
            ))}
        </Container>
    );
};
function StepItem({step, isOuterLayer}: StepItemProps) {
    const isCompleted = step.status === 'success' || step.status === 'failed';
    const [expanded, setExpanded] = useState(true);
    const [isTextTruncated, setIsTextTruncated] = useState(false);
    const titleRef = useRef<HTMLDivElement>(null);

    useEffect(
        () => {
            if (isCompleted) {
                setExpanded(false);
            }
        },
        [isCompleted]
    );

    useEffect(
        () => {
            const checkTruncation = () => {
                if (titleRef.current) {
                    const element = titleRef.current;
                    setIsTextTruncated(element.scrollWidth > element.clientWidth);
                }
            };

            checkTruncation();
            window.addEventListener('resize', checkTruncation);
            return () => window.removeEventListener('resize', checkTruncation);
        },
        [step.title]
    );

    const hasContent = (step?.steps || step?.elements) as unknown as boolean;

    const handleToggle = () => {
        if (hasContent) {
            setExpanded(!expanded);
        }
    };

    const renderStatusBadge = () => {
        if (step.status === 'failed') {
            return (
                <StatusBadge isOuterLayer={isOuterLayer}>
                    失败
                </StatusBadge>
            );
        }
        return null;
    };

    return (
        <StepContainer isOuterLayer={isOuterLayer}>
            <StepHeader isOuterLayer={isOuterLayer} onClick={handleToggle}>
                {renderExpandIcon(isOuterLayer, hasContent, expanded)}
                <StepTitleWrapper>
                    {renderStepIcon(step, isOuterLayer)}
                    <StepTitle
                        ref={titleRef}
                        hasTooltip={isTextTruncated}
                        title={isTextTruncated ? step.title : undefined}
                    >
                        {step.title}
                    </StepTitle>
                    {isOuterLayer && renderStatusBadge()}
                </StepTitleWrapper>
                <Flex gap={8}>
                    {!isOuterLayer && renderStatusBadge()}
                    {renderInnerExpandIcon(isOuterLayer, hasContent, expanded)}
                </Flex>
            </StepHeader>
            {renderStepContent(step, expanded, hasContent)}
        </StepContainer>
    );
}
